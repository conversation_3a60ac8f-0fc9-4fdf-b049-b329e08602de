packaging
flask == 3.0.3
werkzeug == 3.0.6
flask-restx >= 1.3.0, < 2.0.0
pandas == 2.2.3
python-multipart == 0.0.18
cryptography>=35.0
psycopg[binary]
waitress >= 1.4.4
pymongo[srv] == 4.8.0
psutil~=7.0
sqlalchemy >= 2.0.0, < 3.0.0
psycopg2-binary  # This is required for using sqlalchemy with postgres
alembic >= 1.3.3
redis >=5.0.0, < 6.0.0
walrus==0.9.3
flask-compress >= 1.0.0
appdirs >= 1.0.0
mindsdb-sql-parser ~= 0.11.1
pydantic == 2.9.2
mindsdb-evaluator == 0.0.18
duckdb ~= 1.2.1
requests == 2.32.4
dateparser==1.2.0
dill == 0.3.6
numpy
pytz
botocore
boto3 >= 1.34.131
python-dateutil
scikit-learn==1.5.2
hierarchicalforecast~=0.4.0
langchain==0.3.7
langchain-community==0.3.5
langchain-core==0.3.35
langchain-experimental==0.3.3
langchain-nvidia-ai-endpoints==0.3.3
langchain-openai==0.3.6
langchain-anthropic==0.2.4
langchain-text-splitters==0.3.5
langchain-google-genai>=2.0.0
langchain_writer==0.3.0 # Required for Writer agent
lark
lxml==5.3.0 # Required for knowledge base webpage embeddings
pgvector==0.3.6 # Required for knowledge bases
prometheus-client==0.20.0
sentry-sdk[flask] == 2.14.0
openai<2.0.0,>=1.58.1
pyaml==23.12.0
mcp~=1.10.1
anyio>=4.5          # for MCP server, should match 'mcp' requirements
starlette>=0.27     # for MCP server, should match 'mcp' requirements
fastapi>=0.110.0, <1.0.0  # Required for LiteLLM server
uvicorn>=0.30.0, <1.0.0   # Required for LiteLLM server

# files reading
pymupdf==1.25.2
filetype
charset-normalizer
openpyxl # used by pandas to read txt and xlsx files
aipdf==0.0.5
pyarrow<=19.0.0 # used by pandas to read feather files in Files handler


# A2A server
click==8.1.8
httpx==0.28.1
sse-starlette==2.3.3
typing-extensions==4.13.2
python-dotenv==1.1.0
jwcrypto==1.5.6
pyjwt==2.10.1
pydantic_core>=2.23.2
